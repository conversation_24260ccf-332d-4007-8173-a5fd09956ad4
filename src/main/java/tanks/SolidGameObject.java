package tanks;

import tanks.obstacle.Face;
import tanks.obstacle.ISolidObject;

public abstract class SolidGameObject extends GameObject implements ISolidObject
{
    public Face[] faces;

    public abstract double getSize();

    @Override
    public Face[] getFaces()
    {
        if (this.faces == null)
            this.updateFaces();
        return this.faces;
    }

    public boolean isFaceValid(Face f)
    {
        return true;
    }

    public void updateFaces()
    {
        if (this.faces == null)
            this.faces = new Face[4];

        for (int i = 0; i < 4; i++)
            this.faces[i] = new Face(this, Direction.fromIndex(i), true, true);

        double s = this.getSize();

        for (int i = 0; i < 4; i++)
        {
            Face f = this.faces[i];
            f.startX = this.posX + s * (Chunk.x1[i] - 0.5);
            f.startY = this.posY + s * (Chunk.y1[i] - 0.5);
            f.endX = this.posX + s * (Chunk.x2[i] - 0.5);
            f.endY = this.posY + s * (Chunk.y2[i] - 0.5);
            f.valid = this.isFaceValid(f);
        }
    }
}
