package tanks.obstacle;

import tanks.Direction;

public class Face implements Comparable<Face>
{
    public double startX, startY, endX, endY;

    public final Direction direction;
    public boolean solidTank, solidBullet;
    public boolean valid = true;

    public ISolidObject owner;

    public Face(ISolidObject o, Direction direction, boolean tank, boolean bullet)
    {
        this.owner = o;
        this.direction = direction;
        this.solidTank = tank;
        this.solidBullet = bullet;
    }

    public Face(ISolidObject o, double x1, double y1, double x2, double y2, Direction direction, boolean tank, boolean bullet)
    {
        this(o, direction, tank, bullet);
        update(x1, y1, x2, y2);
    }

    public int compareTo(Face f)
    {
        int cx = Double.compare(this.startX, f.startX);
        int cy = Double.compare(this.startY, f.startY);

        if (this.direction.nonZeroX())
            return cx != 0 ? cx : cy;
        return cy != 0 ? cy : cx;
    }

    public void update(double x1, double y1, double x2, double y2)
    {
        this.startX = x1;
        this.startY = y1;
        this.endX = x2;
        this.endY = y2;

        validate();
    }

    public void validate()
    {
        if (startX == endX && startY == endY)
            return;

        if (this.direction.nonZeroY())
        {
            if (this.startX == this.endX)
                throw new RuntimeException("Face has zero width");
            if (this.startY != this.endY)
                throw new RuntimeException("Face is not horizontal");
        }
        else
        {
            if (this.startY == this.endY)
                throw new RuntimeException("Face has zero height");
            if (this.startX != this.endX)
                throw new RuntimeException("Face is not vertical");
        }
    }

    public String toString()
    {
        if (this.direction.nonZeroY())
            return String.format("%.2f-%.2f %.2f", this.startX, this.endX, this.startY);
        else
            return String.format("%.2f %.2f-%.2f", this.startX, this.startY, this.endY);
    }
}
