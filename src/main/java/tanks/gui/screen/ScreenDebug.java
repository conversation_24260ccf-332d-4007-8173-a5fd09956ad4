package tanks.gui.screen;

import tanks.Drawing;
import tanks.Game;
import tanks.gui.Button;
import tanks.replay.ReplayIO;
import tanks.tank.TankPlayer;

import java.util.function.BooleanSupplier;
import java.util.function.Consumer;
import java.util.ArrayList;

public class ScreenDebug extends Screen
{
    // Inner class to handle button registry
    private static class ButtonRegistry
    {
        public Button button;
        public String labelText;
        public BooleanSupplier getter;
        public Consumer<Boolean> setter;

        // Static variables for automatic layout
        private static ScreenDebug currentScreen;
        private static int currentIndex;

        public ButtonRegistry(double x, double y, double width, double height, String labelText,
                            BooleanSupplier getter, Consumer<Boolean> setter)
        {
            this.labelText = labelText;
            this.getter = getter;
            this.setter = setter;

            this.button = new Button(x, y, width, height, "", () -> {
                boolean currentValue = this.getter.getAsBoolean();
                this.setter.accept(!currentValue);
                this.updateText();
            });

            this.updateText();
        }

        /**
         * Static function to register a new button with automatic layout
         * @param labelText The text label for the button
         * @param getter Function to get the current boolean value
         * @param setter Function to set the boolean value
         * @return The created ButtonRegistry object
         */
        public static ButtonRegistry register(String labelText, BooleanSupplier getter, Consumer<Boolean> setter)
        {
            if (currentScreen == null)
                throw new IllegalStateException("ButtonRegistry.register() called before initialization");

            // Calculate position automatically
            double yPos = Drawing.drawing.interfaceSizeY / 2 + currentScreen.objYSpace * (-0.5 + currentIndex * 0.5);
            double xPos = (currentIndex % 2 == 0) ?
                Drawing.drawing.interfaceSizeX / 2 - currentScreen.objXSpace / 2 :
                Drawing.drawing.interfaceSizeX / 2 + currentScreen.objXSpace / 2;

            // Create the ButtonRegistry object
            ButtonRegistry registry = new ButtonRegistry(xPos, yPos, currentScreen.objWidth, currentScreen.objHeight,
                labelText, getter, setter);

            // Add to the screen's registry and increment index
            currentScreen.buttonRegistry.add(registry);
            currentIndex++;

            return registry;
        }

        /**
         * Initialize the static registration system
         * @param screen The ScreenDebug instance to register buttons for
         */
        public static void initializeRegistration(ScreenDebug screen)
        {
            currentScreen = screen;
            currentIndex = 0;
        }

        public void updateText()
        {
            if (this.getter.getAsBoolean())
                this.button.setText(this.labelText, ScreenOptions.onText);
            else
                this.button.setText(this.labelText, ScreenOptions.offText);
        }

        public void update()
        {
            this.button.update();
        }

        public void draw()
        {
            this.button.draw();
        }
    }

    // Button registry ArrayList
    private ArrayList<ButtonRegistry> buttonRegistry;

    Button back = new Button(Drawing.drawing.interfaceSizeX / 2, Drawing.drawing.interfaceSizeY / 2 + this.objYSpace * 4, this.objWidth, this.objHeight, "Back", () -> Game.screen = new ScreenTitle());
    Button test = new Button(Drawing.drawing.interfaceSizeX / 2 - this.objXSpace / 2, Drawing.drawing.interfaceSizeY / 2 - this.objYSpace * 1.5, this.objWidth, this.objHeight, "Test stuff", () -> Game.screen = new ScreenTestDebug());

    public ScreenDebug()
    {
        this.music = "menu_options.ogg";
        this.musicID = "menu";

        // Initialize button registry ArrayList
        this.buttonRegistry = new ArrayList<>();

        // Initialize the static registration system
        ButtonRegistry.initializeRegistration(this);

        // Register all debug buttons using the static function
        ButtonRegistry.register("Destroy cheat: ", () -> TankPlayer.enableDestroyCheat, (value) -> TankPlayer.enableDestroyCheat = value);
        ButtonRegistry.register("First person: ", () -> Game.firstPerson, (value) -> Game.firstPerson = value);
        ButtonRegistry.register("Invulnerable: ", () -> Game.invulnerable, (value) -> Game.invulnerable = value);
        ButtonRegistry.register("Immersive camera: ", () -> Game.followingCam, (value) -> Game.followingCam = value);
        ButtonRegistry.register("Fancy lighting: ", () -> Game.fancyLights, (value) -> Game.fancyLights = value);
        ButtonRegistry.register("Show tank IDs: ", () -> Game.showTankIDs, (value) -> Game.showTankIDs = value);
        ButtonRegistry.register("Show pathfinding: ", () -> Game.showPathfinding, (value) -> Game.showPathfinding = value);
        ButtonRegistry.register("Draw faces: ", () -> Game.showHitboxes, (value) -> Game.showHitboxes = value);
        ButtonRegistry.register("Immutable faces: ", () -> Game.immutableFaces, (value) -> Game.immutableFaces = value);
        ButtonRegistry.register("Trace rays: ", () -> Game.traceAllRays, (value) -> Game.traceAllRays = value);
    }

    @Override
    public void update()
    {
        for (ButtonRegistry registry : buttonRegistry)
            registry.update();

        test.update();
        back.update();
    }

    @Override
    public void draw()
    {
        this.drawDefaultBackground();
        Drawing.drawing.setInterfaceFontSize(this.titleSize);
        Drawing.drawing.setColor(0, 0, 0);
        Drawing.drawing.displayInterfaceText(Drawing.drawing.interfaceSizeX / 2, Drawing.drawing.interfaceSizeY / 2 - 210, "Debug menu");

        for (ButtonRegistry registry : buttonRegistry)
            registry.draw();

        test.draw();
        back.draw();
    }

    @Override
    public void onFilesDropped(String... filePaths)
    {
        ReplayIO.read(filePaths[0]).loadAndPlay();
    }
}
